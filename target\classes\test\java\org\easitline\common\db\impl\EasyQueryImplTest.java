package org.easitline.common.db.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.easitline.common.db.ConnectionMetaData;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * EasyQueryImpl重构后的测试用例
 * 验证功能正确性和性能
 */
@RunWith(MockitoJUnitRunner.class)
public class EasyQueryImplTest {
    
    @Mock
    private Connection mockConnection;
    
    @Mock
    private PreparedStatement mockStatement;
    
    @Mock
    private ResultSet mockResultSet;
    
    @Mock
    private ConnectionMetaData mockMetaData;
    
    private EasyQueryImpl easyQuery;
    
    @Before
    public void setUp() throws SQLException {
        // 使用模拟连接创建EasyQueryImpl
        easyQuery = new EasyQueryImpl(mockConnection);
        
        // 设置模拟行为
        when(mockConnection.prepareStatement(anyString())).thenReturn(mockStatement);
        when(mockStatement.executeQuery()).thenReturn(mockResultSet);
        when(mockStatement.executeUpdate()).thenReturn(1);
    }
    
    @Test
    public void testQueryForString() throws SQLException {
        // 准备测试数据
        when(mockResultSet.next()).thenReturn(true).thenReturn(false);
        when(mockResultSet.getString(1)).thenReturn("test_value");
        when(mockResultSet.getMetaData()).thenReturn(mock(java.sql.ResultSetMetaData.class));
        when(mockResultSet.getMetaData().getColumnCount()).thenReturn(1);
        when(mockResultSet.getMetaData().getColumnLabel(1)).thenReturn("TEST_COLUMN");
        
        // 执行测试
        String result = easyQuery.queryForString("SELECT test_column FROM test_table WHERE id = ?", "123");
        
        // 验证结果
        assertEquals("test_value", result);
        verify(mockStatement).setString(1, "123");
        verify(mockStatement).executeQuery();
    }
    
    @Test
    public void testQueryForInt() throws SQLException {
        // 准备测试数据
        when(mockResultSet.next()).thenReturn(true).thenReturn(false);
        when(mockResultSet.getString(1)).thenReturn("42");
        when(mockResultSet.getMetaData()).thenReturn(mock(java.sql.ResultSetMetaData.class));
        when(mockResultSet.getMetaData().getColumnCount()).thenReturn(1);
        when(mockResultSet.getMetaData().getColumnLabel(1)).thenReturn("COUNT");
        
        // 执行测试
        int result = easyQuery.queryForInt("SELECT COUNT(*) FROM test_table", new Object[0]);
        
        // 验证结果
        assertEquals(42, result);
        verify(mockStatement).executeQuery();
    }
    
    @Test
    public void testQueryForExist() throws SQLException {
        // 准备测试数据
        when(mockResultSet.next()).thenReturn(true).thenReturn(false);
        when(mockResultSet.getString(1)).thenReturn("1");
        when(mockResultSet.getMetaData()).thenReturn(mock(java.sql.ResultSetMetaData.class));
        when(mockResultSet.getMetaData().getColumnCount()).thenReturn(1);
        when(mockResultSet.getMetaData().getColumnLabel(1)).thenReturn("COUNT");
        
        // 执行测试
        boolean result = easyQuery.queryForExist("SELECT COUNT(*) FROM test_table WHERE id = ?", "123");
        
        // 验证结果
        assertTrue(result);
        verify(mockStatement).setString(1, "123");
    }
    
    @Test
    public void testExecuteUpdate() throws SQLException {
        // 执行测试
        int result = easyQuery.executeUpdate("UPDATE test_table SET name = ? WHERE id = ?", "new_name", "123");
        
        // 验证结果
        assertEquals(1, result);
        verify(mockStatement).setString(1, "new_name");
        verify(mockStatement).setString(2, "123");
        verify(mockStatement).executeUpdate();
    }
    
    @Test
    public void testExecute() throws SQLException {
        // 执行测试
        easyQuery.execute("INSERT INTO test_table (name, value) VALUES (?, ?)", "test_name", "test_value");
        
        // 验证调用
        verify(mockStatement).setString(1, "test_name");
        verify(mockStatement).setString(2, "test_value");
        verify(mockStatement).executeUpdate();
    }
    
    @Test
    public void testTransactionManagement() throws SQLException {
        // 测试事务开始
        easyQuery.begin();
        verify(mockConnection).setAutoCommit(false);
        
        // 测试事务提交
        easyQuery.commit();
        verify(mockConnection).commit();
        verify(mockConnection).setAutoCommit(true);
    }
    
    @Test
    public void testConfigurationSync() throws SQLException {
        // 测试配置同步
        easyQuery.setTimeout(30);
        easyQuery.setMaxRow(5000);
        easyQuery.setConvertField(1);
        easyQuery.setSubQuery(true);
        
        // 验证配置已设置（通过getter方法）
        assertEquals(30, easyQuery.getTimeout());
        assertEquals(true, easyQuery.isSubQuery());
    }
    
    @Test
    public void testParameterSetting() throws SQLException {
        // 测试不同类型的参数设置
        easyQuery.executeUpdate("UPDATE test_table SET col1=?, col2=?, col3=?, col4=? WHERE id=?", 
                                123,                    // Integer
                                456L,                   // Long
                                78.9,                   // Double
                                "string_value",         // String
                                "id_value"              // String
        );
        
        // 验证参数设置
        verify(mockStatement).setInt(1, 123);
        verify(mockStatement).setLong(2, 456L);
        verify(mockStatement).setDouble(3, 78.9);
        verify(mockStatement).setString(4, "string_value");
        verify(mockStatement).setString(5, "id_value");
    }
    
    @Test
    public void testErrorHandling() throws SQLException {
        // 模拟SQL异常
        when(mockStatement.executeUpdate()).thenThrow(new SQLException("Test SQL Exception"));
        
        // 验证异常被正确抛出
        try {
            easyQuery.executeUpdate("UPDATE test_table SET name = ?", "test");
            fail("Expected SQLException");
        } catch (SQLException e) {
            assertEquals("Test SQL Exception", e.getMessage());
        }
    }
}
