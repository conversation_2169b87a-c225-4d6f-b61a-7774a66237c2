package org.easitline.common.db.helper;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

import org.apache.log4j.Logger;
import org.easitline.common.db.ConnectionMetaData;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.log.JDBCLogger;

/**
 * 数据库连接管理工具类
 * 统一处理连接获取、Statement创建、资源释放等操作
 * 
 * <AUTHOR> Team
 */
public class ConnectionHelper {
    
    /**
     * 创建PreparedStatement并设置相关属性
     * 
     * @param conn 数据库连接
     * @param sql SQL语句
     * @param params 参数数组
     * @param maxRow 最大行数限制
     * @param timeout 超时时间
     * @return PreparedStatement
     * @throws SQLException 创建失败
     */
    public static PreparedStatement createPreparedStatement(Connection conn, String sql, Object[] params, 
                                                          int maxRow, int timeout) throws SQLException {
        PreparedStatement stmt = conn.prepareStatement(sql);
        setStatementProperties(stmt, maxRow, timeout);
        return stmt;
    }
    
    /**
     * 创建Statement并设置相关属性
     * 
     * @param conn 数据库连接
     * @param maxRow 最大行数限制
     * @param timeout 超时时间
     * @return Statement
     * @throws SQLException 创建失败
     */
    public static Statement createStatement(Connection conn, int maxRow, int timeout) throws SQLException {
        Statement stmt = conn.createStatement();
        setStatementProperties(stmt, maxRow, timeout);
        return stmt;
    }
    
    /**
     * 设置Statement的通用属性
     * 
     * @param stmt Statement对象
     * @param maxRow 最大行数限制，0表示不限制
     * @param timeout 超时时间
     * @throws SQLException 设置失败
     */
    public static void setStatementProperties(Statement stmt, int maxRow, int timeout) throws SQLException {
        if (stmt != null) {
            if (maxRow > 0) {
                // 只有当maxRow大于0时才设置限制，0表示不限制
                stmt.setMaxRows(maxRow);
            }
            stmt.setQueryTimeout(timeout);
        }
    }
    
    /**
     * 设置PreparedStatement的参数
     * 
     * @param stmt PreparedStatement
     * @param params 参数数组
     * @throws SQLException 设置失败
     */
    public static void setParameters(PreparedStatement stmt, Object[] params) throws SQLException {
        if (params != null && stmt != null) {
            for (int i = 1; i <= params.length; i++) {
                setParameter(stmt, i, params[i - 1]);
            }
        }
    }
    
    /**
     * 设置单个参数
     * 
     * @param stmt PreparedStatement
     * @param index 参数索引（从1开始）
     * @param paramObj 参数值
     * @throws SQLException 设置失败
     */
    public static void setParameter(PreparedStatement stmt, int index, Object paramObj) throws SQLException {
        if (paramObj instanceof Integer) {
            stmt.setInt(index, ((Integer) paramObj).intValue());
        } else if (paramObj instanceof Long) {
            stmt.setLong(index, ((Long) paramObj).longValue());
        } else if (paramObj instanceof Double) {
            stmt.setDouble(index, ((Double) paramObj).doubleValue());
        } else if (paramObj instanceof java.sql.Date) {
            stmt.setDate(index, (java.sql.Date) paramObj);
        } else if (paramObj instanceof java.sql.Timestamp) {
            stmt.setTimestamp(index, (java.sql.Timestamp) paramObj);
        } else if (paramObj instanceof Float) {
            stmt.setFloat(index, ((Float) paramObj).floatValue());
        } else if (paramObj instanceof String) {
            stmt.setString(index, (String) paramObj);
        } else {
            stmt.setObject(index, paramObj);
        }
    }
    
    /**
     * 安全关闭数据库资源
     * 
     * @param rs ResultSet
     * @param stmt Statement
     * @param conn Connection
     * @param cacheConnection 缓存的事务连接，如果不为null则不关闭conn
     */
    public static void closeResources(ResultSet rs, Statement stmt, Connection conn, Connection cacheConnection) {
        try {
            if (rs != null) {
                rs.close();
            }
        } catch (Exception ex) {
            JDBCLogger.getLogger().error(ex, ex);
        }
        
        try {
            if (stmt != null) {
                stmt.close();
            }
        } catch (Exception ex) {
            JDBCLogger.getLogger().error(ex, ex);
        }
        
        // 如果有缓存连接（事务中），则不关闭连接
        if (cacheConnection != null) {
            return;
        }
        
        try {
            if (conn != null) {
                conn.close();
            }
        } catch (Exception ex) {
            JDBCLogger.getLogger().error(ex, ex);
        }
    }
    
    /**
     * 关闭连接（用于事务结束时）
     * 
     * @param conn 连接
     * @param logger 日志记录器
     */
    public static void closeConnection(Connection conn, Logger logger) {
        try {
            if (conn != null) {
                conn.close();
            }
        } catch (Exception ex) {
            if (logger != null) {
                logger.error("关闭连接失败: " + ex.getMessage());
            }
            JDBCLogger.getLogger().error(ex, ex);
        }
    }
    
    /**
     * 为SQL添加数据库特定的超时提示
     * 
     * @param sql 原始SQL
     * @param dbType 数据库类型
     * @param timeout 超时时间（秒）
     * @return 添加提示后的SQL
     */
    public static String addTimeoutHint(String sql, DBTypes dbType, int timeout) {
        if (dbType == DBTypes.DAMENG) {
            String hint = String.format("select /*+ MAX_EXECUTION_TIME(%d) */ ", timeout * 1000);
            if (sql.startsWith("select")) {
                sql = sql.replaceFirst("select", hint);
            }
            if (sql.startsWith("SELECT")) {
                sql = sql.replaceFirst("SELECT", hint);
            }
        }
        
        if (dbType == DBTypes.ORACLE) {
            String hint = String.format("select /*+ MAX_DURATION(%d) */ ", timeout);
            if (sql.startsWith("select")) {
                sql = sql.replaceFirst("select", hint);
            }
            if (sql.startsWith("SELECT")) {
                sql = sql.replaceFirst("SELECT", hint);
            }
        }
        
        return sql;
    }
    
    /**
     * 验证连接是否有效
     * 
     * @param conn 数据库连接
     * @throws SQLException 如果连接无效
     */
    public static void validateConnection(Connection conn) throws SQLException {
        if (conn == null) {
            throw new SQLException("数据库连接为null");
        }
        if (conn.isClosed()) {
            throw new SQLException("数据库连接已关闭");
        }
    }
}
