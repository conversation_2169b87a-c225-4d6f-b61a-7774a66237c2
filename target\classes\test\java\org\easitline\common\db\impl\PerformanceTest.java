package org.easitline.common.db.impl;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.easitline.common.db.EasyRow;
import org.junit.Before;
import org.junit.Test;

/**
 * 性能测试
 * 比较重构前后的性能差异
 */
public class PerformanceTest {
    
    private EasyQueryImpl easyQuery;
    private Connection testConnection;
    
    @Before
    public void setUp() throws SQLException {
        // 使用内存数据库进行测试
        testConnection = DriverManager.getConnection("jdbc:h2:mem:testdb", "sa", "");
        easyQuery = new EasyQueryImpl(testConnection);
        
        // 创建测试表
        easyQuery.execute("CREATE TABLE test_table (id INT PRIMARY KEY, name VARCHAR(100), value VARCHAR(100))");
        
        // 插入测试数据
        for (int i = 1; i <= 1000; i++) {
            easyQuery.execute("INSERT INTO test_table VALUES (?, ?, ?)", 
                            i, "name_" + i, "value_" + i);
        }
    }
    
    @Test
    public void testQueryPerformance() throws SQLException {
        long startTime = System.currentTimeMillis();
        
        // 执行100次查询
        for (int i = 0; i < 100; i++) {
            List<EasyRow> results = easyQuery.queryForList("SELECT * FROM test_table WHERE id < ?", new Object[]{100});
            assert results.size() > 0;
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("Query Performance Test:");
        System.out.println("100 queries executed in " + duration + " ms");
        System.out.println("Average time per query: " + (duration / 100.0) + " ms");
        
        // 性能断言：100次查询应该在合理时间内完成（比如5秒）
        assert duration < 5000 : "Query performance is too slow: " + duration + " ms";
    }
    
    @Test
    public void testUpdatePerformance() throws SQLException {
        long startTime = System.currentTimeMillis();
        
        // 执行100次更新
        for (int i = 1; i <= 100; i++) {
            int result = easyQuery.executeUpdate("UPDATE test_table SET value = ? WHERE id = ?", 
                                                "updated_value_" + i, i);
            assert result == 1;
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("Update Performance Test:");
        System.out.println("100 updates executed in " + duration + " ms");
        System.out.println("Average time per update: " + (duration / 100.0) + " ms");
        
        // 性能断言：100次更新应该在合理时间内完成
        assert duration < 3000 : "Update performance is too slow: " + duration + " ms";
    }
    
    @Test
    public void testBatchPerformance() throws SQLException {
        long startTime = System.currentTimeMillis();
        
        // 准备批量数据
        List<Object[]> paramsList = new ArrayList<>();
        for (int i = 1001; i <= 1100; i++) {
            paramsList.add(new Object[]{i, "batch_name_" + i, "batch_value_" + i});
        }
        
        // 执行批量插入
        int[] results = easyQuery.executeBatch("INSERT INTO test_table VALUES (?, ?, ?)", paramsList);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("Batch Performance Test:");
        System.out.println("100 batch inserts executed in " + duration + " ms");
        System.out.println("Average time per batch insert: " + (duration / 100.0) + " ms");
        
        // 验证结果
        assert results.length == 100;
        for (int result : results) {
            assert result == 1;
        }
        
        // 性能断言：批量操作应该比单个操作快
        assert duration < 1000 : "Batch performance is too slow: " + duration + " ms";
    }
    
    @Test
    public void testMemoryUsage() throws SQLException {
        // 记录初始内存使用
        Runtime runtime = Runtime.getRuntime();
        runtime.gc(); // 强制垃圾回收
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 执行大量查询操作
        for (int i = 0; i < 1000; i++) {
            List<EasyRow> results = easyQuery.queryForList("SELECT * FROM test_table LIMIT 10", new Object[0]);
            // 不保持引用，让GC可以回收
            results = null;
        }
        
        // 强制垃圾回收并检查内存使用
        runtime.gc();
        Thread.yield(); // 让GC有时间运行
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = finalMemory - initialMemory;
        
        System.out.println("Memory Usage Test:");
        System.out.println("Initial memory: " + (initialMemory / 1024 / 1024) + " MB");
        System.out.println("Final memory: " + (finalMemory / 1024 / 1024) + " MB");
        System.out.println("Memory increase: " + (memoryIncrease / 1024 / 1024) + " MB");
        
        // 内存增长应该在合理范围内（比如不超过50MB）
        assert memoryIncrease < 50 * 1024 * 1024 : "Memory usage increased too much: " + (memoryIncrease / 1024 / 1024) + " MB";
    }
    
    @Test
    public void testConnectionManagement() throws SQLException {
        long startTime = System.currentTimeMillis();
        
        // 测试事务性能
        easyQuery.begin();
        
        for (int i = 2001; i <= 2100; i++) {
            easyQuery.execute("INSERT INTO test_table VALUES (?, ?, ?)", 
                            i, "trans_name_" + i, "trans_value_" + i);
        }
        
        easyQuery.commit();
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("Transaction Performance Test:");
        System.out.println("100 transactional inserts executed in " + duration + " ms");
        
        // 验证数据已提交
        int count = easyQuery.queryForInt("SELECT COUNT(*) FROM test_table WHERE id >= 2001", new Object[0]);
        assert count == 100;
        
        // 事务操作应该在合理时间内完成
        assert duration < 2000 : "Transaction performance is too slow: " + duration + " ms";
    }
}
