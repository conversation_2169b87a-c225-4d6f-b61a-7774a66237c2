package org.easitline.common.db.impl;

import static org.junit.Assert.*;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

/**
 * 集成测试
 * 验证重构后的完整功能
 */
public class IntegrationTest {
    
    private EasyQueryImpl easyQuery;
    private Connection testConnection;
    
    @Before
    public void setUp() throws SQLException {
        // 使用内存数据库进行测试
        testConnection = DriverManager.getConnection("jdbc:h2:mem:integrationtest", "sa", "");
        easyQuery = new EasyQueryImpl(testConnection);
        
        // 创建测试表
        easyQuery.execute("CREATE TABLE users (id INT PRIMARY KEY, username VARCHAR(50), email VARCHAR(100), age INT)");
        easyQuery.execute("CREATE TABLE orders (id INT PRIMARY KEY, user_id INT, product VARCHAR(100), amount DECIMAL(10,2))");
    }
    
    @After
    public void tearDown() throws SQLException {
        if (testConnection != null && !testConnection.isClosed()) {
            testConnection.close();
        }
    }
    
    @Test
    public void testCompleteWorkflow() throws SQLException {
        // 1. 插入用户数据
        easyQuery.execute("INSERT INTO users VALUES (?, ?, ?, ?)", 1, "john_doe", "<EMAIL>", 25);
        easyQuery.execute("INSERT INTO users VALUES (?, ?, ?, ?)", 2, "jane_smith", "<EMAIL>", 30);
        easyQuery.execute("INSERT INTO users VALUES (?, ?, ?, ?)", 3, "bob_wilson", "<EMAIL>", 35);
        
        // 2. 验证数据插入
        int userCount = easyQuery.queryForInt("SELECT COUNT(*) FROM users", new Object[0]);
        assertEquals(3, userCount);
        
        // 3. 查询单个用户
        String username = easyQuery.queryForString("SELECT username FROM users WHERE id = ?", 1);
        assertEquals("john_doe", username);
        
        // 4. 查询用户列表
        List<EasyRow> users = easyQuery.queryForList("SELECT * FROM users ORDER BY id", new Object[0]);
        assertEquals(3, users.size());
        assertEquals("john_doe", users.get(0).getColumnValue("USERNAME"));
        assertEquals("jane_smith", users.get(1).getColumnValue("USERNAME"));
        
        // 5. 更新用户信息
        int updateResult = easyQuery.executeUpdate("UPDATE users SET age = ? WHERE id = ?", 26, 1);
        assertEquals(1, updateResult);
        
        // 6. 验证更新结果
        int newAge = easyQuery.queryForInt("SELECT age FROM users WHERE id = ?", 1);
        assertEquals(26, newAge);
        
        // 7. 插入订单数据
        easyQuery.execute("INSERT INTO orders VALUES (?, ?, ?, ?)", 1, 1, "Laptop", 999.99);
        easyQuery.execute("INSERT INTO orders VALUES (?, ?, ?, ?)", 2, 1, "Mouse", 29.99);
        easyQuery.execute("INSERT INTO orders VALUES (?, ?, ?, ?)", 3, 2, "Keyboard", 79.99);
        
        // 8. 复杂查询 - 联表查询
        List<EasyRow> userOrders = easyQuery.queryForList(
            "SELECT u.username, o.product, o.amount FROM users u JOIN orders o ON u.id = o.user_id WHERE u.id = ?", 
            new Object[]{1}
        );
        assertEquals(2, userOrders.size());
        assertEquals("Laptop", userOrders.get(0).getColumnValue("PRODUCT"));
        assertEquals("Mouse", userOrders.get(1).getColumnValue("PRODUCT"));
        
        // 9. 聚合查询
        double totalAmount = Double.parseDouble(easyQuery.queryForString(
            "SELECT SUM(amount) FROM orders WHERE user_id = ?", 1
        ));
        assertEquals(1029.98, totalAmount, 0.01);
        
        // 10. 检查记录是否存在
        boolean userExists = easyQuery.queryForExist("SELECT 1 FROM users WHERE username = ?", "john_doe");
        assertTrue(userExists);
        
        boolean nonExistentUser = easyQuery.queryForExist("SELECT 1 FROM users WHERE username = ?", "non_existent");
        assertFalse(nonExistentUser);
    }
    
    @Test
    public void testTransactionIntegrity() throws SQLException {
        // 测试事务回滚
        easyQuery.begin();
        
        try {
            // 插入一些数据
            easyQuery.execute("INSERT INTO users VALUES (?, ?, ?, ?)", 10, "test_user", "<EMAIL>", 20);
            easyQuery.execute("INSERT INTO orders VALUES (?, ?, ?, ?)", 10, 10, "Test Product", 100.0);
            
            // 验证数据在事务中存在
            boolean userExists = easyQuery.queryForExist("SELECT 1 FROM users WHERE id = ?", 10);
            assertTrue(userExists);
            
            // 模拟错误并回滚
            easyQuery.rollback();
            
            // 验证数据已回滚
            userExists = easyQuery.queryForExist("SELECT 1 FROM users WHERE id = ?", 10);
            assertFalse(userExists);
            
        } catch (SQLException e) {
            easyQuery.rollback();
            throw e;
        }
        
        // 测试事务提交
        easyQuery.begin();
        
        try {
            easyQuery.execute("INSERT INTO users VALUES (?, ?, ?, ?)", 11, "commit_user", "<EMAIL>", 25);
            easyQuery.commit();
            
            // 验证数据已提交
            boolean userExists = easyQuery.queryForExist("SELECT 1 FROM users WHERE id = ?", 11);
            assertTrue(userExists);
            
        } catch (SQLException e) {
            easyQuery.rollback();
            throw e;
        }
    }
    
    @Test
    public void testEasyRecordOperations() throws SQLException {
        // 创建EasyRecord
        EasyRecord userRecord = new EasyRecord("users", new String[]{"id"});
        userRecord.set("id", 20);
        userRecord.set("username", "record_user");
        userRecord.set("email", "<EMAIL>");
        userRecord.set("age", 28);
        
        // 保存记录
        boolean saveResult = easyQuery.save(userRecord);
        assertTrue(saveResult);
        
        // 验证保存结果
        String savedUsername = easyQuery.queryForString("SELECT username FROM users WHERE id = ?", 20);
        assertEquals("record_user", savedUsername);
        
        // 查找记录
        Map<String, String> foundRecord = easyQuery.findById(userRecord);
        assertNotNull(foundRecord);
        assertEquals("record_user", foundRecord.get("USERNAME"));
        assertEquals("<EMAIL>", foundRecord.get("EMAIL"));
        
        // 更新记录
        userRecord.set("age", 29);
        boolean updateResult = easyQuery.update(userRecord);
        assertTrue(updateResult);
        
        // 验证更新结果
        int updatedAge = easyQuery.queryForInt("SELECT age FROM users WHERE id = ?", 20);
        assertEquals(29, updatedAge);
        
        // 删除记录
        boolean deleteResult = easyQuery.deleteById(userRecord);
        assertTrue(deleteResult);
        
        // 验证删除结果
        boolean recordExists = easyQuery.queryForExist("SELECT 1 FROM users WHERE id = ?", 20);
        assertFalse(recordExists);
    }
    
    @Test
    public void testPaginationAndLimits() throws SQLException {
        // 插入大量测试数据
        for (int i = 100; i < 200; i++) {
            easyQuery.execute("INSERT INTO users VALUES (?, ?, ?, ?)", 
                            i, "user_" + i, "user" + i + "@example.com", 20 + (i % 50));
        }
        
        // 测试分页查询
        List<EasyRow> page1 = easyQuery.queryForList("SELECT * FROM users WHERE id >= 100 ORDER BY id", 
                                                    new Object[0], 1, 10);
        assertEquals(10, page1.size());
        assertEquals("100", page1.get(0).getColumnValue("ID"));
        
        List<EasyRow> page2 = easyQuery.queryForList("SELECT * FROM users WHERE id >= 100 ORDER BY id", 
                                                    new Object[0], 2, 10);
        assertEquals(10, page2.size());
        assertEquals("110", page2.get(0).getColumnValue("ID"));
        
        // 测试最大行数限制
        easyQuery.setMaxRow(5);
        List<EasyRow> limitedResults = easyQuery.queryForList("SELECT * FROM users WHERE id >= 100 ORDER BY id", 
                                                            new Object[0]);
        assertEquals(5, limitedResults.size());
    }
}
