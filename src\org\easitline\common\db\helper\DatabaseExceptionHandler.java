package org.easitline.common.db.helper;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;

import org.apache.log4j.Logger;
import org.easitline.common.db.ConnectionMetaData;
import org.easitline.common.db.log.JDBCErrorLogger;

/**
 * 数据库异常处理工具类
 * 统一处理数据库操作中的异常、重试机制和日志记录
 * 
 * <AUTHOR> Team
 */
public class DatabaseExceptionHandler {
    
    /**
     * 默认重试次数
     */
    public static final int DEFAULT_RETRY_COUNT = 3;
    
    /**
     * 查询操作重试次数
     */
    public static final int QUERY_RETRY_COUNT = 2;
    
    /**
     * 安全记录错误日志（处理logger为null的情况）
     * 
     * @param logger 日志记录器，可能为null
     * @param message 错误消息
     * @param ex 异常对象
     */
    public static void safeLogError(Logger logger, String message, Exception ex) {
        if (logger != null) {
            logger.error(message + ": " + ex.getMessage());
        }
        // 总是记录到JDBC错误日志
        JDBCErrorLogger.getLogger().error(message + ": " + ex.getMessage());
    }
    
    /**
     * 安全记录错误日志（仅消息）
     * 
     * @param logger 日志记录器，可能为null
     * @param message 错误消息
     */
    public static void safeLogError(Logger logger, String message) {
        if (logger != null) {
            logger.error(message);
        }
        JDBCErrorLogger.getLogger().error(message);
    }
    
    /**
     * 重试执行数据库操作的通用方法
     * 
     * @param <T> 返回类型
     * @param operation 要执行的操作
     * @param retryCount 重试次数
     * @param logger 日志记录器
     * @param operationName 操作名称（用于日志）
     * @return 操作结果
     * @throws SQLException 如果所有重试都失败
     */
    public static <T> T executeWithRetry(
            DatabaseOperation<T> operation, 
            int retryCount, 
            Logger logger, 
            String operationName) throws SQLException {
        
        SQLException lastException = null;
        
        for (int i = 0; i < retryCount; i++) {
            try {
                return operation.execute();
            } catch (SQLException ex) {
                lastException = ex;
                safeLogError(logger, operationName + "失败，第" + (i + 1) + "次重试", ex);
                
                // 如果是最后一次重试，抛出异常
                if (i == retryCount - 1) {
                    throw ex;
                }
            }
        }
        
        // 理论上不会到达这里，但为了安全起见
        throw lastException != null ? lastException : new SQLException(operationName + "失败");
    }
    
    /**
     * 创建PreparedStatement的重试操作
     * 
     * @param connectionProvider 连接提供者
     * @param sql SQL语句
     * @param params 参数
     * @param logger 日志记录器
     * @param closeHandler 失败时的清理处理器
     * @return PreparedStatement
     * @throws SQLException 创建失败
     */
    public static PreparedStatement createPreparedStatementWithRetry(
            ConnectionProvider connectionProvider,
            String sql,
            Object[] params,
            Logger logger,
            CleanupHandler closeHandler) throws SQLException {
        
        return executeWithRetry(() -> {
            try {
                Connection conn = connectionProvider.getConnection();
                return connectionProvider.createPreparedStatement(conn, sql, params);
            } catch (SQLException ex) {
                if (closeHandler != null) {
                    closeHandler.cleanup();
                }
                throw ex;
            }
        }, DEFAULT_RETRY_COUNT, logger, "创建PreparedStatement");
    }
    
    /**
     * 数据库操作接口
     */
    @FunctionalInterface
    public interface DatabaseOperation<T> {
        T execute() throws SQLException;
    }
    
    /**
     * 连接提供者接口
     */
    public interface ConnectionProvider {
        Connection getConnection() throws SQLException;
        PreparedStatement createPreparedStatement(Connection conn, String sql, Object[] params) throws SQLException;
    }
    
    /**
     * 清理处理器接口
     */
    public interface CleanupHandler {
        void cleanup();
    }
    
    /**
     * 验证PreparedStatement不为null
     * 
     * @param stmt PreparedStatement
     * @param operationName 操作名称
     * @throws SQLException 如果stmt为null
     */
    public static void validateStatement(PreparedStatement stmt, String operationName) throws SQLException {
        if (stmt == null) {
            throw new SQLException("无法创建PreparedStatement进行" + operationName + "操作，连接失败");
        }
    }
    
    /**
     * 验证Statement不为null
     * 
     * @param stmt Statement
     * @param operationName 操作名称
     * @throws SQLException 如果stmt为null
     */
    public static void validateStatement(Statement stmt, String operationName) throws SQLException {
        if (stmt == null) {
            throw new SQLException("无法创建Statement进行" + operationName + "操作，连接失败");
        }
    }
}
