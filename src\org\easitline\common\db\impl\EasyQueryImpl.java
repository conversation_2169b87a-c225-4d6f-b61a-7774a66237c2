package org.easitline.common.db.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.ConnectionMetaData;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyCallableStatement;
import org.easitline.common.db.EasyPreparedStatement;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.SqlHelper;
import org.easitline.common.db.log.JDBCErrorLogger;
import org.easitline.common.db.log.JDBCLogger;


/**
 * EasyQuery 的实现类,提供数据库操作的核心功能
 * 主要功能包括:
 * 1. 事务管理(begin/commit/rollback)
 * 2. 数据库连接管理
 * 3. SQL执行(查询/更新/删除等)
 * 4. 结果集映射
 * 5. 分页查询支持
 *
 * <p><b>线程安全说明:</b></p>
 * <ul>
 * <li>本类<b>不是线程安全的</b>，不应在多线程环境中共享同一个实例</li>
 * <li>每个线程应该使用独立的EasyQueryImpl实例</li>
 * <li>事务相关操作(begin/commit/rollback)必须在同一线程中完成</li>
 * <li>如需在多线程环境中使用，请通过EasyQuery.getQuery()为每个线程创建独立实例</li>
 * </ul>
 *
 * <p><b>使用建议:</b></p>
 * <ul>
 * <li>在Web应用中，建议在每个请求处理线程中创建独立的实例</li>
 * <li>避免将实例存储为静态变量或单例模式</li>
 * <li>确保事务操作的完整性，避免连接泄露</li>
 * </ul>
 */

public class EasyQueryImpl extends EasyQuery {
	
	/**
	 * 缓存当前事务使用的数据库连接
	 * 仅在事务开始时设置,事务结束时清除
	 */
	private java.sql.Connection cacheConnection;
	 
	/**
	 * 数据库连接元数据,包含url/用户名/密码等信息
	 */
	private ConnectionMetaData metaData;
    
	/**
	 * 日志记录器
	 */
	private Logger logger;
    
	/**
	 * 字段名大小写转换设置:
	 * 0 - 不转换 
	 * 1 - 转小写
	 * 2 - 转大写(默认)
	 * 3 - 驼峰
	 */
	private int convertField = 2;
    
	/**
	 * 是否显示完整SQL(包含参数值)
	 */
	private boolean showFullSql = false;
    
	/**
	 * 是否为子查询
	 */
	private boolean subQuery = false;
    
	/**
	 * 自定义分页SQL
	 */
	private String pageSql = null;
    
	/**
	 * 数据库schema名称
	 */
	private String schemaName = null;

	/**
	 * 查询返回的最大记录数,0表示不限制
	 * 默认限制20000条
	 */
	private int maxRow = 20000;
	
	/**
	 * SQL执行超时时间,单位秒
	 * 默认30秒
	 */
	private int timeout = 10;
	
	
	public EasyQueryImpl(String url, String user, String password) {
		metaData = new ConnectionMetaData(url, user, password);
	}

	public EasyQueryImpl(String appName, String appDatasourceName) {
		metaData = new ConnectionMetaData(appName, appDatasourceName);
	}

	public EasyQueryImpl(String sysDatasourceName) {
		metaData = new ConnectionMetaData(sysDatasourceName);
	}
	
	public EasyQueryImpl(Connection conn) {
		this.cacheConnection = conn;
	}

	/**
	 * 开始数据库事务
	 * 获取数据库连接并关闭自动提交
	 * 
	 * @throws SQLException 数据库异常
	 */
	public void begin() throws SQLException {
		if(this.cacheConnection!=null){  //如果缓存不为空，则采用当前的连接，防止多次执行begin的操作。
			return;
		}
		if (this.metaData == null) {
			throw new SQLException("数据库连接元数据未初始化，无法开始事务");
		}
		this.cacheConnection = metaData.getConnection();
		if (this.cacheConnection != null && !this.cacheConnection.isClosed()) {
			this.cacheConnection.setAutoCommit(false);
        }
	}

	/*
	 * 
	 * 拼写错误 改用rollback
	 */
	@Deprecated
	public void roolback() throws SQLException {
		rollback();
	}
	
	/**
	 * 回滚数据库事务
	 * 回滚所有未提交的更改并关闭连接
	 * 
	 * @throws SQLException 数据库异常
	 */
	public void rollback() throws SQLException {
		if (this.cacheConnection == null) {
			throw new SQLException("没有活动的事务可以回滚");
		}
		try {
			this.cacheConnection.rollback();
			this.cacheConnection.setAutoCommit(true);
		} catch (SQLException ex) {
			throw ex;
		} finally {
			closeConnection(this.cacheConnection, null);
		}
	}

	/**
	 * 提交数据库事务
	 * 提交所有更改并关闭连接
	 * 
	 * @throws SQLException 数据库异常
	 */
	public void commit() throws SQLException {
		if (this.cacheConnection == null) {
			throw new SQLException("没有活动的事务可以提交");
		}
		try {
			this.cacheConnection.commit();
			this.cacheConnection.setAutoCommit(true);
		} catch (SQLException ex) {
			throw ex;
		} finally {
			closeConnection(this.cacheConnection, null);
		}
	}

	/**
	 * 获取数据库连接
	 * 如果在事务中则返回缓存的连接,否则创建新连接
	 * 
	 * @return 数据库连接
	 * @throws SQLException 数据库异常
	 */
	public java.sql.Connection getConnection() throws SQLException {
		 if(this.cacheConnection != null) return this.cacheConnection;
		 return this.metaData.getConnection();
	}

	/**
	 * 创建预编译语句对象
	 * 
	 * @param sql SQL语句
	 * @return 预编译语句对象
	 * @throws SQLException 数据库异常
	 */
	public EasyPreparedStatement preparedStatement(String sql) throws SQLException {
		return new EasyPreparedStatement(this.getConnection().prepareStatement(sql));
	}
	
	/**
	 * 
	 * @param sql
	 * @return
	 * @throws SQLException
	 */
	public EasyCallableStatement callableStatement(String sql) throws SQLException {
		return new EasyCallableStatement(this.getConnection().prepareCall(sql));
	}

	/**
	 * 设置查询返回的最大记录数
	 * 如果参数为0表示不限制记录数，负数无效使用默认值20000
	 *
	 * @param maxCount 最大记录数，0表示不限制
	 */
	public void setMaxRow(int maxCount) {
		if (maxCount < 0) {
			// 负数无效，使用默认值
			this.maxRow = 20000;
		} else if (maxCount == 0) {
			// 0表示不限制记录数
			this.maxRow = 0;
		} else {
			// 正数直接设置
			this.maxRow = maxCount;
		}
	}
	
	public void setLogger(Logger logger) {
		this.logger=logger;
	}
	
	public int getTimeout() {
		return timeout;
	}

	public void setTimeout(int timeout) {
		if(timeout>180) {
			this.timeout = 180;
			JDBCLogger.getLogger().warn("setTimeout("+timeout+") - "+SqlHelper.getStackTrace(Thread.currentThread().getStackTrace()));
		}else {
			this.timeout = timeout;
		}  
	}
	
	public boolean isShowFullSql() {
		return showFullSql;
	}

	public void setShowFullSql(boolean showFullSql) {
		this.showFullSql = showFullSql;
	}
	
	public String getPageSql() {
		return pageSql;
	}

	public void setPageSql(String pageSql) {
		this.pageSql = pageSql;
	}
	
	
	public boolean isSubQuery() {
		return subQuery;
	}

	public void setSubQuery(boolean subQuery) {
		this.subQuery = subQuery;
	}

	/**
	 * 设置字段返回大小写
	 * @param toUpperCase
	 */
	public void setConvertField(int convertField) {
		this.convertField = convertField;
	}
	
	/**
	 *  设置表前缀
	 * @param schemaName
	 */
	public void setSchemaName(String schemaName) {
		this.schemaName = schemaName;
	}

	/**
	 * 设置表前缀 (兼容旧方法名)
	 * @param schameName
	 * @deprecated 请使用 setSchemaName 方法
	 */
	@Deprecated
	public void setSchameName(String schameName) {
		this.setSchemaName(schameName);
	}

	/**
	 * 执行 update 、delete、create的等批操作
	 * 
	 * @param sqlList
	 *            ArrayList -> sql 保存每一条要执行的sql语句
	 * @throws SQLException
	 *             执行失败抛出异常
	 */

	public int[] executeBatch(List<String> sqlList) throws SQLException {
		java.sql.Connection conn = null;
		java.sql.Statement stmt = null;
		try {
			for(int i = 0;i<3;i++){
				try {
					conn = this.getConnection();
					stmt = this.createStatement(conn);
					break;
				} catch (Exception ex) {
					if (this.logger != null) {
						this.logger.error("创建Statement失败,原因: "+ex.getMessage());
					}
					JDBCErrorLogger.getLogger().error("创建Statement失败,原因: "+ex.getMessage());
					this.close(null, stmt, conn);
				}
			}
			for (int i = 0; i < sqlList.size(); i++) {
				stmt.addBatch(sqlList.get(i));
			}
			return stmt.executeBatch();
		} catch (SQLException ex) {
			throw ex;
		} finally {
			this.close(null, stmt, conn);
		}
	}
	
	public void setStmtMaxRow(java.sql.Statement stmt) throws SQLException {
		if(stmt!=null && this.maxRow > 0) {
			// 只有当maxRow大于0时才设置限制，0表示不限制
			stmt.setMaxRows(this.maxRow);
		}
	}

	/**
	 * 创建statement
	 * 
	 * @param conn
	 *            Connection
	 * @throws SQLException
	 * @return Statement
	 */
	private java.sql.Statement createStatement(Connection conn) throws SQLException {
		java.sql.Statement stmt = conn.createStatement();
		this.setStmtMaxRow(stmt);
		stmt.setQueryTimeout(this.timeout);
		return stmt;
	}

	/**
	 * 创建statement
	 * 
	 * @param conn
	 * Connection
	 * @throws SQLException
	 * @return Statement
	 */
	private java.sql.PreparedStatement createPreparedStatement(Connection conn,String sql,Object[] params)throws SQLException {
		java.sql.PreparedStatement stmt = conn.prepareStatement(sql);
		this.setStmtMaxRow(stmt);
		stmt.setQueryTimeout(this.timeout);
		return stmt;
	}
	

	/**
	 * 释放连接
	 * 
	 * @param rs
	 * ResultSet
	 * @param stmt
	 * Statement
	 * @param conn
	 * Connection
	 */
	private void close(java.sql.ResultSet rs, java.sql.Statement stmt, java.sql.Connection conn) {
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			 JDBCLogger.getLogger().error(ex,ex);
		}
		try {
			if (stmt != null)
				stmt.close();
		} catch (Exception ex) {
			JDBCLogger.getLogger().error(ex,ex);
		}
		if(this.cacheConnection!=null) return;
		try {
			// 如果连接不为空，并且事务没有开始。
			if (conn != null )
				conn.close();
		} catch (Exception ex) {
			JDBCLogger.getLogger().error(ex,ex);
		}
	}
	




	@Override
	public boolean queryForExist(String sql, Object... params) throws SQLException {
		if (this.queryForInt(sql, params) > 0) {
			return true;
		}
		return false;
	}

	@Override
	public int queryForInt(String sql, Object... params) throws SQLException {
		EasyRow easyRow = this.queryForRow(sql, params);
		if(easyRow==null||StringUtils.isBlank(easyRow.getColumnValue(1))){
			return 0;
		}
		return Integer.parseInt(easyRow.getColumnValue(1));
	}
 
	@Override
	public String queryForString(String sql, Object... params) throws SQLException {
		EasyRow easyRow = this.queryForRow(sql, params);
		if(easyRow == null) return null;
		String retValue = easyRow.getColumnValue(1);
		if(retValue == null) return "";
		return retValue;
	}

	@Override
	public List<EasyRow> queryForList(String sql, Object... args) throws SQLException {
		return this.queryForList(sql, args, -1, -1);
	}
	


	@Override
	public EasyRow queryForRow(String sql, Object... params) throws SQLException {
		return  this.queryForRow(sql, params,new EasyRowMapperImpl());
	}

	@Override
	public void execute(String sql, Object... params) throws SQLException {
		java.sql.Connection conn = null;
		java.sql.PreparedStatement stmt = null;
		long exetime = System.currentTimeMillis();
		try {
			for(int i = 0;i<3;i++){
				try {
					conn = this.getConnection();
					stmt = this.createPreparedStatement(conn, sql,params);
					break;
				} catch (SQLException ex) {
					if (this.logger != null) {
						this.logger.error("创建PreparedStatement失败,原因: "+ex.getMessage());
					}
					JDBCErrorLogger.getLogger().error("创建PreparedStatement失败,原因: "+ex.getMessage());
					this.close(null, stmt, conn);
					// 如果是最后一次重试，抛出异常
					if (i == 2) {
						throw ex;
					}
				}
			}
			if (stmt == null) {
				throw new SQLException("无法创建PreparedStatement，连接失败");
			}
			if (params != null) {
				for (int i = 1; i <= params.length; i++) {
					this.setParam(stmt, i, params[i - 1]);
				}
			}

			stmt.execute();
		} catch (SQLException ex) {
			SqlHelper.errorLogOut(this.logger,ex, sql, params,metaData);
			throw ex;
		} finally {
			exetime = System.currentTimeMillis()-exetime;
			SqlHelper.timeoutLogOut(this.logger,exetime, sql, params,metaData);
			this.close(null, stmt, conn);
		}
	}
	
	public  Map<String, String> findById(EasyRecord record,String columns) throws SQLException {
		return findByIdLoadColumns(record,columns);
	}
	
	public  Map<String, String> findById(EasyRecord record) throws SQLException {
		return findByIdLoadColumns(record,"*");
	}
	
	private  Map<String, String> findByIdLoadColumns(EasyRecord record, String columns) throws SQLException {
		trimPrimaryKeys(record.getPrimaryKeys());
		String sql =forDbFindById(record.getTableName(),record.getPrimaryKeys(),columns);
		List<Map<String, String>> result=queryForList(sql, record.getPrimaryValues(),new MapRowMapperImpl());
		return result.size() > 0 ? result.get(0) : null;
	}
	
	private String forDbFindById(String tableName, String[] pKeys,String columns) {
		tableName = tableName.trim();
		trimPrimaryKeys(pKeys);
		StringBuilder sql = new StringBuilder("select ");
		columns = columns.trim();
		if ("*".equals(columns)) {
			sql.append("*");
		}
		else {
			String[] arr = columns.split(",");
			for (int i=0; i<arr.length; i++) {
				if (i > 0) {
					sql.append(",");
				}
				sql.append("").append(arr[i].trim()).append("");
			}
		}
		sql.append(" from ");
		sql.append(tableName);
		sql.append(" where ");
		
		for (int i=0; i<pKeys.length; i++) {
			if (i > 0) {
				sql.append(" and ");
			}
			sql.append("").append(pKeys[i]).append(" = ? ");
		}
		return sql.toString();
	}
	
	private StringBuilder forDbDeleteById(EasyRecord record) {
		String[] pKeys=record.getPrimaryKeys();
		trimPrimaryKeys(pKeys);
		StringBuilder sql = new StringBuilder("delete from ").append(record.getTableName()).append(" where ");
		for (int i=0; i<pKeys.length; i++) {
			if (i > 0) {
				sql.append(" and ");
			}
			sql.append("").append(pKeys[i]).append(" = ? ");
		}
		String condition = record.getCondition();
		if(condition!=null) {
			sql.append(condition);
		}
		return sql;
	}
	
	private void trimPrimaryKeys(String[] pKeys) {
		if(pKeys!=null){
			for (int i=0; i<pKeys.length; i++) {
				pKeys[i] = pKeys[i].toUpperCase().trim();
			}
		}
	}
	/**
	 * Do not delete the String[] pKeys parameter, the element of pKeys needs to trim()
	 */
	private void forDbSave(String tableName, String[] pKeys, EasyRecord record, StringBuilder sql, List<Object> paras) {
		tableName = tableName.trim();
		trimPrimaryKeys(pKeys);	// important
		
		sql.append("insert into ");
		sql.append(tableName).append("(");
		StringBuilder temp = new StringBuilder();
		temp.append(") values(");
		
		EasySQL easySQL=record.getEasySQL();
		if(easySQL!=null){
			sql.append(easySQL.getSQL());
			paras.addAll(Arrays.asList(easySQL.getParams()));
		}
		
		String[] nullKeys = record.getNullKeys();
		if(nullKeys!=null) {
			for(int i=0,len=nullKeys.length;i<len;i++ ) {
				String key = nullKeys[i];
				record.remove(key.toUpperCase());
				sql.append(key);
				if(len!=i+1) {
					sql.append(", ");
					temp.append("?, ");
				}else {
					temp.append("? ");
				}
				paras.add(null);
			}
		}
		
		for (Entry<String, Object> e: record.getColumns().entrySet()) {
			if (paras.size() > 0) {
				sql.append(", ");
				temp.append(", ");
			}
			sql.append("").append(e.getKey()).append("");
			temp.append("?");
			paras.add(e.getValue());
		}
		sql.append(temp.toString()).append(")");
	}
	
	private void forDbUpdate(String tableName, String[] pKeys, EasyRecord record, StringBuilder sql, List<Object> paras) {
		tableName = tableName.trim();
		trimPrimaryKeys(pKeys);
		
		sql.append("update ").append(tableName).append(" set ");
		EasySQL easySQL=record.getEasySQL();
		if(easySQL!=null){
			sql.append(easySQL.getSQL());
			paras.addAll(Arrays.asList(easySQL.getParams()));
		}
		String[] nullKeys = record.getNullKeys();
		if(nullKeys!=null) {
			for(int i=0,len=nullKeys.length;i<len;i++ ) {
				String key = nullKeys[i];
				record.remove(key.toUpperCase());
				sql.append("").append(key).append(" = ? ");
				if(len!=i+1) {
					sql.append(", ");
				}
				paras.add(null);
			}
		}
		
		for (Entry<String, Object> e: record.getColumns().entrySet()) {
			String colName = e.getKey();
			if (!record.isPrimaryKey(colName, pKeys)) {
				if (paras.size() > 0) {
					sql.append(", ");
				}
				sql.append("").append(colName).append(" = ? ");
				paras.add(e.getValue());
				
			}
		}
		sql.append(" where ");
		for (int i=0; i<pKeys.length; i++) {
			if (i > 0) {
				sql.append(" and ");
			}
			sql.append("").append(pKeys[i]).append(" = ? ");
			paras.add(record.get(pKeys[i]));
		}
		String condition = record.getCondition();
		if(condition!=null) {
			sql.append(condition);
		}
	}
	
	@Override
	public boolean update(EasyRecord record) throws SQLException{
		//拼装SQL
		StringBuilder sql = new StringBuilder();
		List<Object> paras = new ArrayList<Object>();
		trimPrimaryKeys(record.getPrimaryKeys());
		forDbUpdate(record.getTableName(),record.getPrimaryKeys(),record,sql,paras);
		//执行新增语句
		int result = 0;
		Object[] params=paras.toArray();
		java.sql.Connection conn = null;
		java.sql.PreparedStatement stmt = null;
		long exetime = System.currentTimeMillis();
		try {
			for(int i = 0;i<3;i++){
				try {
					conn = this.getConnection();
					stmt = this.createPreparedStatement(conn, sql.toString(),params);
					break;
				} catch (Exception ex) {
					if (this.logger != null) {
						this.logger.error("创建PreparedStatement失败,原因: "+ex.getMessage());
					}
					JDBCErrorLogger.getLogger().error("创建PreparedStatement失败,原因: "+ex.getMessage());
					this.close(null, stmt, conn);
				}
			}
			if (params != null) {
				for (int i = 1; i <= params.length; i++) {
					this.setParam(stmt, i, params[i - 1]);
				}
			}

			result=stmt.executeUpdate();
			return result >= 1;
		} catch (SQLException ex) {
			SqlHelper.errorLogOut(this.logger,ex, sql.toString(), params,metaData);
			throw ex;
		} finally {
			exetime = System.currentTimeMillis()-exetime;
			SqlHelper.timeoutLogOut(this.logger,exetime, sql.toString(), params,metaData);
			this.close(null, stmt, conn);
		}
	}
	
	@Override
	public boolean save(EasyRecord record) throws SQLException{
		//拼装SQL
		StringBuilder sql = new StringBuilder();
		List<Object> paras = new ArrayList<Object>();
		trimPrimaryKeys(record.getPrimaryKeys());
		forDbSave(record.getTableName(),record.getPrimaryKeys(),record,sql,paras);
		//执行新增语句
		int result = 0;
		Object[] params=paras.toArray();
		java.sql.Connection conn = null;
		java.sql.PreparedStatement stmt = null;
		long exetime = System.currentTimeMillis();
		try {
			for(int i = 0;i<3;i++){
				try {
					conn = this.getConnection();
					stmt = this.createPreparedStatement(conn, sql.toString(),params);
					break;
				} catch (Exception ex) {
					if (this.logger != null) {
						this.logger.error("创建PreparedStatement失败,原因: "+ex.getMessage());
					}
					JDBCErrorLogger.getLogger().error("创建PreparedStatement失败,原因: "+ex.getMessage());
					this.close(null, stmt, conn);
				}
			}
			
			if (params != null) {
				for (int i = 1; i <= params.length; i++) {
					this.setParam(stmt, i, params[i - 1]);
				}
			}
			
			result=stmt.executeUpdate();
			return result >= 1;
		} catch (SQLException ex) {
			SqlHelper.errorLogOut(this.logger,ex, sql.toString(), params,metaData);
			throw ex;
		} finally {
			exetime = System.currentTimeMillis()-exetime;
			SqlHelper.timeoutLogOut(this.logger,exetime, sql.toString(), params,metaData);
			this.close(null, stmt, conn);
		}
		
	}
	
	
	@Override
	public  boolean deleteById(EasyRecord record) throws SQLException{
		boolean flag=true;//执行结果
		//生成Sql
		StringBuilder sql =forDbDeleteById(record);
		//执行
		Object[] idValues=record.getPrimaryValues();
	 	java.sql.Connection conn = null;
		java.sql.PreparedStatement stmt = null;
		long exetime = System.currentTimeMillis();
		try {
			for(int i = 0;i<3;i++){
				try {
					conn = this.getConnection();
					stmt = this.createPreparedStatement(conn, sql.toString(),idValues);
					break;
				} catch (Exception ex) {
					if (this.logger != null) {
						this.logger.error("创建PreparedStatement失败,原因: "+ex.getMessage());
					}
					JDBCErrorLogger.getLogger().error("创建PreparedStatement失败,原因: "+ex.getMessage());
					this.close(null, stmt, conn);
				}
			}
			if (idValues != null) {
				for (int i = 1; i <= idValues.length; i++) {
					this.setParam(stmt, i, idValues[i - 1]);
				}
			}
			flag = stmt.executeUpdate() >= 1;
		} catch (SQLException ex) {
			flag=false;
			SqlHelper.errorLogOut(this.logger,ex, sql.toString(), idValues,metaData);
			throw ex;
		} finally {
			exetime = System.currentTimeMillis()-exetime;
			SqlHelper.timeoutLogOut(this.logger,exetime, sql.toString(), idValues,metaData);
			this.close(null, stmt, conn);
		}
		return flag;
	}

	@Override
	public int executeUpdate(String sql, Object... params) throws SQLException {
		java.sql.Connection conn = null;
		java.sql.PreparedStatement stmt = null;
		long exetime = System.currentTimeMillis();
		try {
			for(int i = 0;i<3;i++){
				try {
					conn = this.getConnection();
					stmt = this.createPreparedStatement(conn, sql,params);
					break;
				} catch (Exception ex) {
					if (this.logger != null) {
						this.logger.error("创建PreparedStatement失败,原因: "+ex.getMessage());
					}
					JDBCErrorLogger.getLogger().error("创建PreparedStatement失败,原因: "+ex.getMessage());
					this.close(null, stmt, conn);
				}
			}
			if (params != null) {
				for (int i = 1; i <= params.length; i++) {
					this.setParam(stmt, i, params[i - 1]);
				}
			}
			
			return stmt.executeUpdate();
		} catch (SQLException ex) {
			SqlHelper.errorLogOut(this.logger,ex, sql, params,metaData);
			throw ex;
		} finally {
			exetime = System.currentTimeMillis()-exetime;
			SqlHelper.timeoutLogOut(this.logger,exetime, sql, params,metaData);
			this.close(null, stmt, conn);
		}

	}

	/**
	 * 执行查询
	 * 
	 * @param sql
	 * @param args
	 *            参数对象
	 * @param pageNum
	 *            当前页
	 * @param pageSize
	 *            每页记录数
	 * @return
	 * @throws SQLException
	 */
	@Override
	public List<EasyRow> queryForList(String sql, Object[] params, int pageNum, int pageSize) throws SQLException {
		return queryForList(sql,params,pageNum,pageSize,new EasyRowMapperImpl());
	}

	/**
	 * 把ResultSet对象转化成List<EasyRow>
	 * 
	 * @param rs
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private <T> List<T> toList(ResultSet rs, EasyRowMapper<T> rowMapper,int convertField) throws SQLException {
		List<T> rows = new ArrayList<T>();
		while (rs.next()) {
			 rows.add((T) rowMapper.mapRow(rs,convertField));
		}
		return rows;
	}

	/**
	 * 设定分页信息
	 * 
	 * @param sql
	 * @param pageNum
	 *            当前页
	 * @param pageSize
	 *            每页显示记录数
	 * @return
	 */
	private String setPagination(String sql, int pageNum, int pageSize) {
		return PaginationSqlBuilder.setPagination(sql, pageNum, pageSize, this.getTypes(), this.getPageSql(), this.isSubQuery());
	}
	
	/**
	 * 针对不同的数据库类型，设置sql的执行超时时间，通过增加hnit来实现。 如：mysql-> MAX_EXECUTION_TIME(ms) 
	 * @param sql
	 * @return
	 */
	private String setDBQueryTimeoutHnit(String sql ){
	   sql = StringUtils.trim(sql);
	   DBTypes dbType = this.getTypes();
	   // 根据数据库类型生成分页SQL
	   if (dbType == DBTypes.MYSQL) {
		   String hint = String.format("select /*+ MAX_EXECUTION_TIME(%d) */ ", this.timeout*1000);
		   if(sql.startsWith("select")){
			  sql = sql.replaceFirst("select", hint);
		   }
		   if(sql.startsWith("SELECT")){
			   sql = sql.replaceFirst("SELECT", hint);
		   }
	   }
	   
	   if (dbType == DBTypes.DAMENG) {
		   String hint = String.format("select /*+ MAX_EXECUTION_TIME(%d) */ ", this.timeout*1000);
		   if(sql.startsWith("select")){
			  sql = sql.replaceFirst("select", hint);
		   }
		   if(sql.startsWith("SELECT")){
			   sql = sql.replaceFirst("SELECT", hint);
		   }
	   }
	   
	   if (dbType == DBTypes.ORACLE) {
		   String hint = String.format("select /*+ MAX_DURATION(%d) */ ", this.timeout);
		   if(sql.startsWith("select")){
			  sql = sql.replaceFirst("select", hint);
		   }
		   if(sql.startsWith("SELECT")){
			   sql = sql.replaceFirst("SELECT", hint);
		   }
	   }
	   
	   return sql;
	 }

	/**
	 * 设定查询参数
	 * 
	 * @param stmt
	 * @param index
	 * @param paramObj
	 * @throws SQLException
	 */
	private void setParam(PreparedStatement stmt, int index, Object paramObj) throws SQLException {
		if (paramObj instanceof Integer) {
			stmt.setInt(index, ((Integer) paramObj).intValue());
		} else if (paramObj instanceof Long) {
			stmt.setLong(index, ((Long) paramObj).longValue());
		} else if (paramObj instanceof Double) {
			stmt.setDouble(index, ((Double) paramObj).doubleValue());
		} else if (paramObj instanceof java.sql.Date) {
			stmt.setDate(index, (java.sql.Date) paramObj);
		} else if (paramObj instanceof java.sql.Timestamp) {
			stmt.setTimestamp(index, (java.sql.Timestamp) paramObj);
		} else if (paramObj instanceof Float) {
			stmt.setFloat(index, ((Float) paramObj).floatValue());
		} else if (paramObj instanceof String) {
			stmt.setString(index, (String) paramObj);
		} else {
			stmt.setObject(index, paramObj);
		}
	}
	
	
	@Override
	public DBTypes getTypes() {  //通过连接来判断数据库类型
		return this.metaData.getDriverType();
		
	}
	

	@Override
	public <T> List<T> queryForList(String sql, Object[] params, EasyRowMapper<T> rowMapper) throws SQLException {
		
		return this.queryForList(sql, params, -1,-1,rowMapper,99);  //一般查询，不带分页
	}
	
	private <T> List<T> queryForList(String sql, Object[] params, int pageNum, int pageSize, EasyRowMapper<T> rowMapper,int type) throws SQLException {
		return this.queryForListProxy(sql, params,pageNum,pageSize,rowMapper,type);

	}
	
	private <T> List<T> queryForListProxy(String sql, Object[] params, int pageNum, int pageSize, EasyRowMapper<T> rowMapper,int type) throws SQLException {
		java.sql.Connection conn = null;
		java.sql.PreparedStatement stmt = null;
		long exetime = System.currentTimeMillis();
		sql = this.setDBQueryTimeoutHnit(sql);
		// 设定分页信息
		if(type == 2){
			sql = this.setPagination(sql, pageNum, pageSize);
		}
		List<T> list = null;
		try {
			for(int i = 0;i<2;i++){
				try {
					conn = this.getConnection();
					stmt = this.createPreparedStatement(conn, sql,params);
					break;
				} catch (Exception ex) {
					if (this.logger != null) {
						this.logger.error("创建PreparedStatement失败,原因: "+ex.getMessage());
					}
					JDBCErrorLogger.getLogger().error("创建PreparedStatement失败,原因: "+ex.getMessage());
					this.close(null, stmt, conn);
				}
			}
			if(type == 1)  stmt.setMaxRows(1);  //设置查询返回记录数为1
			
			if (params != null) {
				for (int i = 1; i <= params.length; i++) {
					this.setParam(stmt, i, params[i - 1]);
				}
			}
			
			ResultSet rs = stmt.executeQuery();
			if(rs!=null){
				list = this.toList(rs, rowMapper,this.convertField);
			}
			
			if(list.size()>10000){
				SqlHelper.listSizeLogOut(this.logger,list.size(), sql, params);
			}
			
		} catch (SQLException ex) {
			SqlHelper.errorLogOut(this.logger,ex, sql, params,metaData);
			throw ex;
		} finally {
			exetime = System.currentTimeMillis()-exetime;
			SqlHelper.timeoutLogOut(this.logger,exetime, sql, params,metaData);
			this.close(null, stmt, conn);
		}
		return list;
	}
	
	@Override
	public <T> List<T> queryForList(String sql, Object[] params, int pageNum, int pageSize, EasyRowMapper<T> rowMapper) throws SQLException {
		 return this.queryForList(sql, params, pageNum, pageSize, rowMapper,2);
	}

	@Override
	public <T> T queryForRow(String sql, Object[] params, EasyRowMapper<T> rowMapper) throws SQLException {
		List<T> list =  this.queryForList(sql, params, -1, -1, rowMapper,1);
		if(list == null) return null;
		if(list.size()>0) return list.get(0);
		return null;
	}


	/**
	 * 执行批量查询 
	 */
	public int[] executeBatch(String sql, List<Object[]> paramsList) throws SQLException {
		java.sql.Connection conn = null;
		PreparedStatement ps = null ;
		long exetime = System.currentTimeMillis();
		try {
			conn = this.getConnection();
			conn.setAutoCommit(false);
			ps   = conn.prepareStatement(sql);
			for (int i = 0; i < paramsList.size(); i++) {
				Object[] params = paramsList.get(i);
//				if(isDebug&&i==0){
//					if (logger == null) {
//						logger=JDBCLogger.getLogger();
//					}
//					if(isShowFullSql()){
//						logger.info("SQL:"+SqlHelper.showFullSql(sql, params));
//					}else{
//						logger.info("SQL:" + sql + "," + SqlHelper.paramToString(params));
//					}
//				}
				for(int j = 0;j < params.length;j++){
					ps.setObject(j+1, params[j]);
				}
				ps.addBatch(); 
			}
			int[] result =  ps.executeBatch();
			conn.commit();
			return result;
		} catch (SQLException ex) {
			if(conn!=null) conn.rollback();
			throw ex;
		} finally {
			exetime = System.currentTimeMillis()-exetime;
			if(paramsList!=null) {
				SqlHelper.timeoutLogOut(this.logger,exetime, sql, new Object[] {paramsList},metaData);
			}else {
				SqlHelper.timeoutLogOut(this.logger,exetime, sql, null,metaData);
			}
			this.close(null, ps, conn);
		}
	}
	
	/**
	 * 关闭数据库连接和相关资源
	 * @param conn 数据库连接
	 * @param ex 异常信息(可选)
	 */
	private void closeConnection(Connection conn, Exception ex) {
		try {
			if (conn != null) {
				conn.close(); 
			}
		} catch (Exception e) {
			if(logger != null) {
				logger.error(e, e);
			} else {
				JDBCLogger.getLogger().error(e, e);
			}
		}
		this.cacheConnection = null;
	}
}
